/**
 * 动画相关的可视化脚本节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 播放动画节点 (142)
 */
export class PlayAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称',
      defaultValue: ''
    });

    // 添加循环播放输入
    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否循环播放',
      defaultValue: false
    });

    // 添加混合时间输入
    this.addInput({
      name: 'blendTime',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '混合时间（秒）',
      defaultValue: 0.3
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '播放完成'
    });

    this.addOutput({
      name: 'started',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '开始播放'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功播放'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;
    const loop = inputs.loop || false;
    const blendTime = inputs.blendTime || 0.3;

    if (!entity || !animationName) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 播放动画
      animationComponent.play(animationName, loop, blendTime);
      this.setOutputValue('success', true);

      // 先触发开始流程
      this.triggerFlow('started');

      // 如果不是循环播放，监听动画完成事件
      if (!loop) {
        animationComponent.once('animationComplete', () => {
          this.triggerFlow('completed');
        });
      }

      return null; // 不立即触发完成流程
    } catch (error) {
      console.error('播放动画失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 停止动画节点 (143)
 */
export class StopAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入（可选，如果不指定则停止所有动画）
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称（可选）',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '停止完成'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功停止'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;

    if (!entity) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 停止动画
      if (animationName) {
        animationComponent.stop(animationName);
      } else {
        animationComponent.stopAll();
      }

      this.setOutputValue('success', true);
      return 'completed';
    } catch (error) {
      console.error('停止动画失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 暂停动画节点 (144)
 */
export class PauseAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入（可选）
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称（可选）',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '暂停完成'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功暂停'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;

    if (!entity) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 暂停动画
      if (animationName) {
        animationComponent.pause(animationName);
      } else {
        animationComponent.pauseAll();
      }

      this.setOutputValue('success', true);
      return 'completed';
    } catch (error) {
      console.error('暂停动画失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 恢复动画节点 (145)
 */
export class ResumeAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入（可选）
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称（可选）',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '恢复完成'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功恢复'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;

    if (!entity) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 恢复动画
      if (animationName) {
        animationComponent.resume(animationName);
      } else {
        animationComponent.resumeAll();
      }

      this.setOutputValue('success', true);
      return 'completed';
    } catch (error) {
      console.error('恢复动画失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 设置动画速度节点 (146)
 */
export class SetAnimationSpeedNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入（可选）
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称（可选）',
      defaultValue: ''
    });

    // 添加速度输入
    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '播放速度',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '设置完成'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功设置'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;
    const speed = inputs.speed || 1.0;

    if (!entity) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 设置动画速度
      if (animationName) {
        animationComponent.setSpeed(animationName, speed);
      } else {
        const animator = animationComponent.getAnimator();
        if (animator) {
          animator.setTimeScale(speed);
        }
      }

      this.setOutputValue('success', true);
      return 'completed';
    } catch (error) {
      console.error('设置动画速度失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 获取动画状态节点 (147)
 */
export class GetAnimationStateNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入（可选）
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称（可选）',
      defaultValue: ''
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isPlaying',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否播放中'
    });

    this.addOutput({
      name: 'isPaused',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否暂停'
    });

    this.addOutput({
      name: 'currentClip',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '当前动画名称'
    });

    this.addOutput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '播放时间（秒）'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '动画总时长（秒）'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '播放进度（0-1）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const entity = this.getInputValue('entity');
    const animationName = this.getInputValue('animationName') as string;

    if (!entity) {
      // 设置默认值
      this.setOutputValue('isPlaying', false);
      this.setOutputValue('isPaused', false);
      this.setOutputValue('currentClip', '');
      this.setOutputValue('time', 0);
      this.setOutputValue('duration', 0);
      this.setOutputValue('progress', 0);
      return null;
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        // 设置默认值
        this.setOutputValue('isPlaying', false);
        this.setOutputValue('isPaused', false);
        this.setOutputValue('currentClip', '');
        this.setOutputValue('time', 0);
        this.setOutputValue('duration', 0);
        this.setOutputValue('progress', 0);
        return null;
      }

      // 获取动画状态
      const isPlaying = animationComponent.isPlaying(animationName);
      const isPaused = animationComponent.isPaused(animationName);
      const currentClip = animationComponent.getCurrentClip() || '';
      const time = animationComponent.getTime(animationName) || 0;
      const duration = animationComponent.getDuration(animationName) || 0;
      const progress = duration > 0 ? time / duration : 0;

      // 设置输出值
      this.setOutputValue('isPlaying', isPlaying);
      this.setOutputValue('isPaused', isPaused);
      this.setOutputValue('currentClip', currentClip);
      this.setOutputValue('time', time);
      this.setOutputValue('duration', duration);
      this.setOutputValue('progress', progress);

      return { isPlaying, isPaused, currentClip, time, duration, progress };
    } catch (error) {
      console.error('获取动画状态失败:', error);
      // 设置默认值
      this.setOutputValue('isPlaying', false);
      this.setOutputValue('isPaused', false);
      this.setOutputValue('currentClip', '');
      this.setOutputValue('time', 0);
      this.setOutputValue('duration', 0);
      this.setOutputValue('progress', 0);
      return null;
    }
  }
}

/**
 * 设置动画时间节点 (148)
 */
export class SetAnimationTimeNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加动画名称输入
    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称',
      defaultValue: ''
    });

    // 添加时间输入
    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '目标时间（秒）',
      defaultValue: 0
    });

    // 添加进度输入（可选，0-1之间）
    this.addInput({
      name: 'progress',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '目标进度（0-1）',
      defaultValue: -1
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '设置完成'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功设置'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const animationName = inputs.animationName;
    const time = inputs.time || 0;
    const progress = inputs.progress;

    if (!entity || !animationName) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 设置动画时间
      if (progress >= 0 && progress <= 1) {
        // 使用进度设置
        const duration = animationComponent.getDuration(animationName);
        if (duration > 0) {
          animationComponent.setTime(animationName, duration * progress);
        }
      } else {
        // 使用绝对时间设置
        animationComponent.setTime(animationName, time);
      }

      this.setOutputValue('success', true);
      return 'completed';
    } catch (error) {
      console.error('设置动画时间失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 动画混合节点 (149)
 */
export class CrossFadeAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '目标实体'
    });

    // 添加源动画名称输入
    this.addInput({
      name: 'fromAnimation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源动画名称',
      defaultValue: ''
    });

    // 添加目标动画名称输入
    this.addInput({
      name: 'toAnimation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '目标动画名称',
      defaultValue: ''
    });

    // 添加混合时间输入
    this.addInput({
      name: 'blendTime',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '混合时间（秒）',
      defaultValue: 0.3
    });

    // 添加混合权重输入
    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '混合权重（0-1）',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '混合完成'
    });

    this.addOutput({
      name: 'started',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '开始混合'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否成功开始混合'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const entity = inputs.entity;
    const fromAnimation = inputs.fromAnimation;
    const toAnimation = inputs.toAnimation;
    const blendTime = inputs.blendTime || 0.3;
    const weight = inputs.weight || 1.0;

    if (!entity || !toAnimation) {
      this.setOutputValue('success', false);
      return 'completed';
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return 'completed';
      }

      // 执行动画混合
      if (fromAnimation) {
        animationComponent.crossFade(fromAnimation, toAnimation, blendTime, weight);
      } else {
        // 如果没有指定源动画，则从当前动画混合到目标动画
        animationComponent.crossFadeToAnimation(toAnimation, blendTime, weight);
      }

      this.setOutputValue('success', true);

      // 先触发开始流程
      this.triggerFlow('started');

      // 监听混合完成事件
      setTimeout(() => {
        this.triggerFlow('completed');
      }, blendTime * 1000);

      return null; // 不立即触发完成流程
    } catch (error) {
      console.error('动画混合失败:', error);
      this.setOutputValue('success', false);
      return 'completed';
    }
  }
}

/**
 * 注册动画节点
 * @param registry 节点注册表
 */
export function registerAnimationNodes(registry: NodeRegistry): void {
  // 注册播放动画节点 (142)
  registry.registerNodeType({
    type: 'animation/play',
    category: NodeCategory.ANIMATION,
    constructor: PlayAnimationNode,
    label: '播放动画',
    description: '播放指定的动画',
    icon: 'play',
    color: '#722ED1',
    tags: ['animation', 'play', 'control']
  });

  // 注册停止动画节点 (143)
  registry.registerNodeType({
    type: 'animation/stop',
    category: NodeCategory.ANIMATION,
    constructor: StopAnimationNode,
    label: '停止动画',
    description: '停止当前播放的动画',
    icon: 'stop',
    color: '#722ED1',
    tags: ['animation', 'stop', 'control']
  });

  // 注册暂停动画节点 (144)
  registry.registerNodeType({
    type: 'animation/pause',
    category: NodeCategory.ANIMATION,
    constructor: PauseAnimationNode,
    label: '暂停动画',
    description: '暂停当前播放的动画',
    icon: 'pause',
    color: '#722ED1',
    tags: ['animation', 'pause', 'control']
  });

  // 注册恢复动画节点 (145)
  registry.registerNodeType({
    type: 'animation/resume',
    category: NodeCategory.ANIMATION,
    constructor: ResumeAnimationNode,
    label: '恢复动画',
    description: '恢复暂停的动画播放',
    icon: 'play',
    color: '#722ED1',
    tags: ['animation', 'resume', 'control']
  });

  // 注册设置动画速度节点 (146)
  registry.registerNodeType({
    type: 'animation/setSpeed',
    category: NodeCategory.ANIMATION,
    constructor: SetAnimationSpeedNode,
    label: '设置动画速度',
    description: '设置动画播放速度',
    icon: 'speed',
    color: '#722ED1',
    tags: ['animation', 'speed', 'control']
  });

  // 注册获取动画状态节点 (147)
  registry.registerNodeType({
    type: 'animation/getState',
    category: NodeCategory.ANIMATION,
    constructor: GetAnimationStateNode,
    label: '获取动画状态',
    description: '获取当前动画的播放状态',
    icon: 'info',
    color: '#722ED1',
    tags: ['animation', 'state', 'info']
  });

  // 注册设置动画时间节点 (148)
  registry.registerNodeType({
    type: 'animation/setTime',
    category: NodeCategory.ANIMATION,
    constructor: SetAnimationTimeNode,
    label: '设置动画时间',
    description: '设置动画播放到指定时间点',
    icon: 'time',
    color: '#722ED1',
    tags: ['animation', 'time', 'control']
  });

  // 注册动画混合节点 (149)
  registry.registerNodeType({
    type: 'animation/crossFade',
    category: NodeCategory.ANIMATION,
    constructor: CrossFadeAnimationNode,
    label: '动画混合',
    description: '在两个动画之间进行混合过渡',
    icon: 'blend',
    color: '#722ED1',
    tags: ['animation', 'blend', 'crossfade']
  });
}
