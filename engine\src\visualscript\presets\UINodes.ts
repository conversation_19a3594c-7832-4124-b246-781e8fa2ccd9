/**
 * 视觉脚本UI节点
 * 提供UI交互相关的节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import type { NodeOptions } from '../nodes/Node';

/**
 * 按钮点击事件节点
 * 监听按钮点击事件
 */
export class ButtonClickNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的按钮元素'
    });
  }

  /**
   * 开始监听事件
   */
  public start(): void {
    const element = this.getInputValue('element') as HTMLElement;
    if (element) {
      element.addEventListener('click', () => {
        this.trigger();
      });
    }
  }
}

/**
 * 创建按钮节点 (229)
 * 创建一个UI按钮
 */
export class CreateButtonNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按钮显示的文本',
      defaultValue: '按钮'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的按钮对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const button = document.createElement('button');
      button.textContent = text;
      button.style.position = 'absolute';
      button.style.left = '10px';
      button.style.top = '10px';
      button.style.zIndex = '1000';

      document.body.appendChild(button);

      this.setOutputValue('button', button);
      this.triggerFlow('flow');
      return button;
    } catch (error) {
      this.setOutputValue('button', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置按钮文本节点 (231)
 * 设置按钮显示的文本
 */
export class SetButtonTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置文本的按钮对象'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '新的按钮文本',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置文本'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const text = this.getInputValue('text') as string;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.textContent = text;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置按钮状态节点 (232)
 * 设置按钮是否可用
 */
export class SetButtonEnabledNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置状态的按钮对象'
    });

    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '按钮是否可用',
      defaultValue: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置状态'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const enabled = this.getInputValue('enabled') as boolean;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.disabled = !enabled;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建文本节点 (233)
 * 创建一个UI文本标签
 */
export class CreateTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '显示的文本内容',
      defaultValue: '文本'
    });

    this.addOutput({
      name: 'textElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的文本元素对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const textElement = document.createElement('div');
      textElement.textContent = text;
      textElement.style.position = 'absolute';
      textElement.style.left = '10px';
      textElement.style.top = '50px';
      textElement.style.zIndex = '1000';

      document.body.appendChild(textElement);

      this.setOutputValue('textElement', textElement);
      this.triggerFlow('flow');
      return textElement;
    } catch (error) {
      this.setOutputValue('textElement', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 输入框变化事件节点
 * 监听输入框内容变化事件
 */
export class InputChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '输入框的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', element.value);
        this.trigger();
      });
    }
  }
}

/**
 * 滑块值变化事件节点
 * 监听滑块值变化事件
 */
export class SliderValueChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的滑块元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滑块的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', parseFloat(element.value));
        this.trigger();
      });
    }
  }
}

/**
 * 设置文本颜色节点 (234)
 * 设置文本元素的颜色
 */
export class SetTextColorNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '文本元素'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '颜色值（CSS格式）',
      defaultValue: '#000000'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置颜色'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const color = this.getInputValue('color') as string;

    if (!element || !color) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.color = color;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本颜色失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置文本大小节点 (235)
 * 设置文本元素的字体大小
 */
export class SetTextSizeNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '文本元素'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '字体大小（像素）',
      defaultValue: 16
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置大小'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const size = this.getInputValue('size') as number;

    if (!element || typeof size !== 'number') {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.fontSize = `${size}px`;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本大小失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建输入框节点 (236)
 * 创建一个文本输入框
 */
export class CreateInputNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'placeholder',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '占位符文本',
      defaultValue: '请输入...'
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '默认值',
      defaultValue: ''
    });

    this.addOutput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的输入框元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const placeholder = this.getInputValue('placeholder') as string;
    const defaultValue = this.getInputValue('defaultValue') as string;

    try {
      const input = document.createElement('input');
      input.type = 'text';
      input.placeholder = placeholder || '请输入...';
      input.value = defaultValue || '';
      input.style.padding = '8px';
      input.style.border = '1px solid #ccc';
      input.style.borderRadius = '4px';

      // 添加到页面（这里应该根据实际需求调整）
      document.body.appendChild(input);

      this.setOutputValue('inputElement', input);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return input;
    } catch (error) {
      console.error('创建输入框失败:', error);
      this.setOutputValue('inputElement', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 获取输入框值节点 (237)
 * 获取输入框的当前值
 */
export class GetInputValueNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '输入框的值'
    });
  }

  public execute(): any {
    const inputElement = this.getInputValue('inputElement') as HTMLInputElement;

    if (!inputElement) {
      this.setOutputValue('value', '');
      return '';
    }

    try {
      const value = inputElement.value || '';
      this.setOutputValue('value', value);
      return value;
    } catch (error) {
      console.error('获取输入框值失败:', error);
      this.setOutputValue('value', '');
      return '';
    }
  }
}

/**
 * 设置输入框值节点 (238)
 * 设置输入框的值
 */
export class SetInputValueNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入框元素'
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要设置的值',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置值'
    });
  }

  public execute(): any {
    const inputElement = this.getInputValue('inputElement') as HTMLInputElement;
    const value = this.getInputValue('value') as string;

    if (!inputElement) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      inputElement.value = value || '';
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置输入框值失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置元素位置节点 (239)
 * 设置UI元素的位置
 */
export class SetElementPositionNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });

    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标（像素）',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标（像素）',
      defaultValue: 0
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置位置'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;

    if (!element) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.position = 'absolute';
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置元素位置失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册UI节点
 * @param registry 节点注册表
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点 (229)
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个UI按钮',
    icon: 'button',
    color: '#EB2F96',
    tags: ['ui', 'button', 'create']
  });

  // 注册按钮点击事件节点 (230)
  registry.registerNodeType({
    type: 'ui/button/onClick',
    category: NodeCategory.UI,
    constructor: ButtonClickNode,
    label: '按钮点击',
    description: '监听按钮点击事件',
    icon: 'click',
    color: '#EB2F96',
    tags: ['ui', 'button', 'click', 'event']
  });

  // 注册设置按钮文本节点 (231)
  registry.registerNodeType({
    type: 'ui/button/setText',
    category: NodeCategory.UI,
    constructor: SetButtonTextNode,
    label: '设置按钮文本',
    description: '设置按钮显示的文本',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'button', 'text', 'set']
  });

  // 注册设置按钮状态节点 (232)
  registry.registerNodeType({
    type: 'ui/button/setEnabled',
    category: NodeCategory.UI,
    constructor: SetButtonEnabledNode,
    label: '设置按钮状态',
    description: '设置按钮是否可用',
    icon: 'toggle',
    color: '#EB2F96',
    tags: ['ui', 'button', 'enabled', 'set']
  });

  // 注册创建文本节点 (233)
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个UI文本标签',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'text', 'create']
  });

  // 注册设置文本颜色节点 (234)
  registry.registerNodeType({
    type: 'ui/text/setColor',
    category: NodeCategory.UI,
    constructor: SetTextColorNode,
    label: '设置文本颜色',
    description: '设置文本元素的颜色',
    icon: 'color',
    color: '#EB2F96',
    tags: ['ui', 'text', 'color', 'set']
  });

  // 注册设置文本大小节点 (235)
  registry.registerNodeType({
    type: 'ui/text/setSize',
    category: NodeCategory.UI,
    constructor: SetTextSizeNode,
    label: '设置文本大小',
    description: '设置文本元素的字体大小',
    icon: 'size',
    color: '#EB2F96',
    tags: ['ui', 'text', 'size', 'font', 'set']
  });

  // 注册创建输入框节点 (236)
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个文本输入框',
    icon: 'input',
    color: '#EB2F96',
    tags: ['ui', 'input', 'create']
  });

  // 注册获取输入框值节点 (237)
  registry.registerNodeType({
    type: 'ui/input/getValue',
    category: NodeCategory.UI,
    constructor: GetInputValueNode,
    label: '获取输入框值',
    description: '获取输入框的当前值',
    icon: 'get',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'get']
  });

  // 注册设置输入框值节点 (238)
  registry.registerNodeType({
    type: 'ui/input/setValue',
    category: NodeCategory.UI,
    constructor: SetInputValueNode,
    label: '设置输入框值',
    description: '设置输入框的值',
    icon: 'set',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'set']
  });

  // 注册设置元素位置节点 (239)
  registry.registerNodeType({
    type: 'ui/element/setPosition',
    category: NodeCategory.UI,
    constructor: SetElementPositionNode,
    label: '设置元素位置',
    description: '设置UI元素的位置',
    icon: 'position',
    color: '#EB2F96',
    tags: ['ui', 'element', 'position', 'set']
  });

  // 注册输入框变化事件节点 (240)
  registry.registerNodeType({
    type: 'ui/input/onChange',
    category: NodeCategory.UI,
    constructor: InputChangeNode,
    label: '输入变化',
    description: '监听输入框内容变化事件',
    icon: 'edit',
    color: '#EB2F96',
    tags: ['ui', 'input', 'change', 'event']
  });

  // 注册滑块值变化事件节点
  registry.registerNodeType({
    type: 'ui/slider/onValueChange',
    category: NodeCategory.UI,
    constructor: SliderValueChangeNode,
    label: '滑块值变化事件',
    description: '监听滑块值变化事件',
    icon: 'slider',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'range', 'change', 'event']
  });
}
