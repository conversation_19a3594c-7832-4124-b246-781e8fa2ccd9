/**
 * 视觉脚本UI节点
 * 提供UI交互相关的节点
 */
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 按钮点击事件节点
 * 监听按钮点击事件
 */
export class ButtonClickNode extends EventNode {
  private element: HTMLElement | null = null;
  private clickHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按钮ID输入
    this.addInput({
      name: 'buttonId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按钮元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '点击时触发'
    });

    // 添加事件信息输出
    this.addOutput({
      name: 'event',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '点击事件信息'
    });

    // 添加时间戳输出
    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '点击时间戳'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const buttonId = String(this.getInputValue('buttonId') || '');
    
    if (buttonId) {
      this.element = document.getElementById(buttonId);
      
      if (this.element) {
        this.clickHandler = (event: Event) => {
          // 设置输出值
          this.setOutputValue('event', {
            type: event.type,
            target: event.target,
            currentTarget: event.currentTarget
          });
          this.setOutputValue('timestamp', Date.now());

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('click', this.clickHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.clickHandler) {
      this.element.removeEventListener('click', this.clickHandler);
      this.element = null;
      this.clickHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 输入框变化事件节点
 * 监听输入框内容变化事件
 */
export class InputChangeNode extends EventNode {
  private element: HTMLInputElement | null = null;
  private changeHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入框ID输入
    this.addInput({
      name: 'inputId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '输入框元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '内容变化时触发'
    });

    // 添加输入值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '输入框当前值'
    });

    // 添加事件信息输出
    this.addOutput({
      name: 'event',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '变化事件信息'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const inputId = String(this.getInputValue('inputId') || '');
    
    if (inputId) {
      this.element = document.getElementById(inputId) as HTMLInputElement;
      
      if (this.element) {
        this.changeHandler = (event: Event) => {
          const target = event.target as HTMLInputElement;
          
          // 设置输出值
          this.setOutputValue('value', target.value);
          this.setOutputValue('event', {
            type: event.type,
            target: event.target,
            currentTarget: event.currentTarget
          });

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('input', this.changeHandler);
        this.element.addEventListener('change', this.changeHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.changeHandler) {
      this.element.removeEventListener('input', this.changeHandler);
      this.element.removeEventListener('change', this.changeHandler);
      this.element = null;
      this.changeHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 滑块值变化事件节点
 * 监听滑块值变化事件
 */
export class SliderValueChangeNode extends EventNode {
  private element: HTMLInputElement | null = null;
  private changeHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加滑块ID输入
    this.addInput({
      name: 'sliderId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '滑块元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '值变化时触发'
    });

    // 添加数值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '滑块当前值'
    });

    // 添加百分比输出
    this.addOutput({
      name: 'percentage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '滑块百分比值'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const sliderId = String(this.getInputValue('sliderId') || '');
    
    if (sliderId) {
      this.element = document.getElementById(sliderId) as HTMLInputElement;
      
      if (this.element && this.element.type === 'range') {
        this.changeHandler = (event: Event) => {
          const target = event.target as HTMLInputElement;
          const value = parseFloat(target.value);
          const min = parseFloat(target.min) || 0;
          const max = parseFloat(target.max) || 100;
          const percentage = ((value - min) / (max - min)) * 100;
          
          // 设置输出值
          this.setOutputValue('value', value);
          this.setOutputValue('percentage', percentage);

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('input', this.changeHandler);
        this.element.addEventListener('change', this.changeHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.changeHandler) {
      this.element.removeEventListener('input', this.changeHandler);
      this.element.removeEventListener('change', this.changeHandler);
      this.element = null;
      this.changeHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 创建按钮节点 (229)
 * 创建一个UI按钮
 */
export class CreateButtonNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'text',
      type: SocketType.STRING,
      label: '文本',
      description: '按钮显示的文本',
      defaultValue: '按钮'
    });

    this.addInput({
      name: 'x',
      type: SocketType.NUMBER,
      label: 'X位置',
      description: '按钮的X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.NUMBER,
      label: 'Y位置',
      description: '按钮的Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'width',
      type: SocketType.NUMBER,
      label: '宽度',
      description: '按钮的宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.NUMBER,
      label: '高度',
      description: '按钮的高度',
      defaultValue: 30
    });

    // 输出插槽
    this.addOutput({
      name: 'button',
      type: SocketType.OBJECT,
      label: '按钮',
      description: '创建的按钮对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    try {
      // 创建按钮元素
      const button = document.createElement('button');
      button.textContent = text;
      button.style.position = 'absolute';
      button.style.left = `${x}px`;
      button.style.top = `${y}px`;
      button.style.width = `${width}px`;
      button.style.height = `${height}px`;
      button.style.zIndex = '1000';

      // 添加到页面
      document.body.appendChild(button);

      this.setOutputValue('button', button);
      this.triggerFlow('flow');
      return button;
    } catch (error) {
      console.error('创建按钮失败:', error);
      this.setOutputValue('button', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置按钮文本节点 (231)
 * 设置按钮显示的文本
 */
export class SetButtonTextNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.OBJECT,
      label: '按钮',
      description: '要设置文本的按钮对象',
      defaultValue: null
    });

    this.addInput({
      name: 'text',
      type: SocketType.STRING,
      label: '文本',
      description: '新的按钮文本',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置文本'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const button = this.getInputValue('button') as HTMLButtonElement;
    const text = this.getInputValue('text') as string;

    // 检查输入值
    if (!button || typeof text !== 'string') {
      console.error('设置按钮文本需要指定按钮对象和文本');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.textContent = text;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置按钮文本失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置按钮状态节点 (232)
 * 设置按钮是否可用
 */
export class SetButtonEnabledNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.OBJECT,
      label: '按钮',
      description: '要设置状态的按钮对象',
      defaultValue: null
    });

    this.addInput({
      name: 'enabled',
      type: SocketType.BOOLEAN,
      label: '启用',
      description: '按钮是否可用',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置状态'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const button = this.getInputValue('button') as HTMLButtonElement;
    const enabled = this.getInputValue('enabled') as boolean;

    // 检查输入值
    if (!button) {
      console.error('设置按钮状态需要指定按钮对象');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.disabled = !enabled;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置按钮状态失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建文本节点 (233)
 * 创建一个UI文本标签
 */
export class CreateTextNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'text',
      type: SocketType.STRING,
      label: '文本',
      description: '显示的文本内容',
      defaultValue: '文本'
    });

    this.addInput({
      name: 'x',
      type: SocketType.NUMBER,
      label: 'X位置',
      description: '文本的X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.NUMBER,
      label: 'Y位置',
      description: '文本的Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'fontSize',
      type: SocketType.NUMBER,
      label: '字体大小',
      description: '文本的字体大小',
      defaultValue: 16
    });

    // 输出插槽
    this.addOutput({
      name: 'textElement',
      type: SocketType.OBJECT,
      label: '文本元素',
      description: '创建的文本元素对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const fontSize = this.getInputValue('fontSize') as number;

    try {
      // 创建文本元素
      const textElement = document.createElement('div');
      textElement.textContent = text;
      textElement.style.position = 'absolute';
      textElement.style.left = `${x}px`;
      textElement.style.top = `${y}px`;
      textElement.style.fontSize = `${fontSize}px`;
      textElement.style.zIndex = '1000';
      textElement.style.pointerEvents = 'none';

      // 添加到页面
      document.body.appendChild(textElement);

      this.setOutputValue('textElement', textElement);
      this.triggerFlow('flow');
      return textElement;
    } catch (error) {
      console.error('创建文本失败:', error);
      this.setOutputValue('textElement', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置文本内容节点 (234)
 * 设置文本标签的内容
 */
export class SetTextContentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'textElement',
      type: SocketType.OBJECT,
      label: '文本元素',
      description: '要设置内容的文本元素',
      defaultValue: null
    });

    this.addInput({
      name: 'text',
      type: SocketType.STRING,
      label: '文本',
      description: '新的文本内容',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置文本内容'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const textElement = this.getInputValue('textElement') as HTMLElement;
    const text = this.getInputValue('text') as string;

    // 检查输入值
    if (!textElement || typeof text !== 'string') {
      console.error('设置文本内容需要指定文本元素和内容');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      textElement.textContent = text;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本内容失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置文本颜色节点 (235)
 * 设置文本的颜色
 */
export class SetTextColorNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'textElement',
      type: SocketType.OBJECT,
      label: '文本元素',
      description: '要设置颜色的文本元素',
      defaultValue: null
    });

    this.addInput({
      name: 'color',
      type: SocketType.STRING,
      label: '颜色',
      description: '文本颜色（CSS颜色值）',
      defaultValue: '#000000'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置文本颜色'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const textElement = this.getInputValue('textElement') as HTMLElement;
    const color = this.getInputValue('color') as string;

    // 检查输入值
    if (!textElement || typeof color !== 'string') {
      console.error('设置文本颜色需要指定文本元素和颜色');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      textElement.style.color = color;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本颜色失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置文本大小节点 (236)
 * 设置文本的字体大小
 */
export class SetTextSizeNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'textElement',
      type: SocketType.OBJECT,
      label: '文本元素',
      description: '要设置大小的文本元素',
      defaultValue: null
    });

    this.addInput({
      name: 'size',
      type: SocketType.NUMBER,
      label: '字体大小',
      description: '文本的字体大小（像素）',
      defaultValue: 16
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置文本大小'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const textElement = this.getInputValue('textElement') as HTMLElement;
    const size = this.getInputValue('size') as number;

    // 检查输入值
    if (!textElement || typeof size !== 'number') {
      console.error('设置文本大小需要指定文本元素和大小');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      textElement.style.fontSize = `${size}px`;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本大小失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建输入框节点 (237)
 * 创建一个输入框
 */
export class CreateInputNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'placeholder',
      type: SocketType.STRING,
      label: '占位符',
      description: '输入框的占位符文本',
      defaultValue: '请输入...'
    });

    this.addInput({
      name: 'x',
      type: SocketType.NUMBER,
      label: 'X位置',
      description: '输入框的X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.NUMBER,
      label: 'Y位置',
      description: '输入框的Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'width',
      type: SocketType.NUMBER,
      label: '宽度',
      description: '输入框的宽度',
      defaultValue: 200
    });

    this.addInput({
      name: 'height',
      type: SocketType.NUMBER,
      label: '高度',
      description: '输入框的高度',
      defaultValue: 30
    });

    // 输出插槽
    this.addOutput({
      name: 'input',
      type: SocketType.OBJECT,
      label: '输入框',
      description: '创建的输入框对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const placeholder = this.getInputValue('placeholder') as string;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    try {
      // 创建输入框元素
      const input = document.createElement('input');
      input.type = 'text';
      input.placeholder = placeholder;
      input.style.position = 'absolute';
      input.style.left = `${x}px`;
      input.style.top = `${y}px`;
      input.style.width = `${width}px`;
      input.style.height = `${height}px`;
      input.style.zIndex = '1000';

      // 添加到页面
      document.body.appendChild(input);

      this.setOutputValue('input', input);
      this.triggerFlow('flow');
      return input;
    } catch (error) {
      console.error('创建输入框失败:', error);
      this.setOutputValue('input', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 获取输入值节点 (238)
 * 获取输入框的当前值
 */
export class GetInputValueNode extends FunctionNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'input',
      type: SocketType.OBJECT,
      label: '输入框',
      description: '要获取值的输入框对象',
      defaultValue: null
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.STRING,
      label: '值',
      description: '输入框的当前值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const input = this.getInputValue('input') as HTMLInputElement;

    // 检查输入值
    if (!input) {
      console.error('获取输入值需要指定输入框对象');
      this.setOutputValue('value', '');
      return '';
    }

    try {
      const value = input.value || '';
      this.setOutputValue('value', value);
      return value;
    } catch (error) {
      console.error('获取输入值失败:', error);
      this.setOutputValue('value', '');
      return '';
    }
  }
}

/**
 * 设置输入值节点 (239)
 * 设置输入框的值
 */
export class SetInputValueNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'input',
      type: SocketType.OBJECT,
      label: '输入框',
      description: '要设置值的输入框对象',
      defaultValue: null
    });

    this.addInput({
      name: 'value',
      type: SocketType.STRING,
      label: '值',
      description: '新的输入框值',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.BOOLEAN,
      label: '成功',
      description: '是否成功设置输入值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const input = this.getInputValue('input') as HTMLInputElement;
    const value = this.getInputValue('value') as string;

    // 检查输入值
    if (!input || typeof value !== 'string') {
      console.error('设置输入值需要指定输入框对象和值');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      input.value = value;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置输入值失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册UI节点
 * @param registry 节点注册表
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点 (229)
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个UI按钮',
    icon: 'button',
    color: '#EB2F96',
    tags: ['ui', 'button', 'create']
  });

  // 注册按钮点击事件节点 (230)
  registry.registerNodeType({
    type: 'ui/button/onClick',
    category: NodeCategory.UI,
    constructor: ButtonClickNode,
    label: '按钮点击',
    description: '监听按钮点击事件',
    icon: 'click',
    color: '#EB2F96',
    tags: ['ui', 'button', 'click', 'event']
  });

  // 注册设置按钮文本节点 (231)
  registry.registerNodeType({
    type: 'ui/button/setText',
    category: NodeCategory.UI,
    constructor: SetButtonTextNode,
    label: '设置按钮文本',
    description: '设置按钮显示的文本',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'button', 'text', 'set']
  });

  // 注册设置按钮状态节点 (232)
  registry.registerNodeType({
    type: 'ui/button/setEnabled',
    category: NodeCategory.UI,
    constructor: SetButtonEnabledNode,
    label: '设置按钮状态',
    description: '设置按钮是否可用',
    icon: 'toggle',
    color: '#EB2F96',
    tags: ['ui', 'button', 'enabled', 'set']
  });

  // 注册创建文本节点 (233)
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个UI文本标签',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'text', 'create']
  });

  // 注册设置文本内容节点 (234)
  registry.registerNodeType({
    type: 'ui/text/setText',
    category: NodeCategory.UI,
    constructor: SetTextContentNode,
    label: '设置文本内容',
    description: '设置文本标签的内容',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'text', 'content', 'set']
  });

  // 注册设置文本颜色节点 (235)
  registry.registerNodeType({
    type: 'ui/text/setColor',
    category: NodeCategory.UI,
    constructor: SetTextColorNode,
    label: '设置文本颜色',
    description: '设置文本的颜色',
    icon: 'color',
    color: '#EB2F96',
    tags: ['ui', 'text', 'color', 'set']
  });

  // 注册设置文本大小节点 (236)
  registry.registerNodeType({
    type: 'ui/text/setSize',
    category: NodeCategory.UI,
    constructor: SetTextSizeNode,
    label: '设置文本大小',
    description: '设置文本的字体大小',
    icon: 'size',
    color: '#EB2F96',
    tags: ['ui', 'text', 'size', 'set']
  });

  // 注册创建输入框节点 (237)
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个输入框',
    icon: 'input',
    color: '#EB2F96',
    tags: ['ui', 'input', 'create']
  });

  // 注册获取输入值节点 (238)
  registry.registerNodeType({
    type: 'ui/input/getValue',
    category: NodeCategory.UI,
    constructor: GetInputValueNode,
    label: '获取输入值',
    description: '获取输入框的当前值',
    icon: 'get',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'get']
  });

  // 注册设置输入值节点 (239)
  registry.registerNodeType({
    type: 'ui/input/setValue',
    category: NodeCategory.UI,
    constructor: SetInputValueNode,
    label: '设置输入值',
    description: '设置输入框的值',
    icon: 'set',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'set']
  });

  // 注册输入框变化事件节点 (240)
  registry.registerNodeType({
    type: 'ui/input/onChange',
    category: NodeCategory.UI,
    constructor: InputChangeNode,
    label: '输入变化',
    description: '监听输入框内容变化事件',
    icon: 'edit',
    color: '#EB2F96',
    tags: ['ui', 'input', 'change', 'event']
  });

  // 注册滑块值变化事件节点
  registry.registerNodeType({
    type: 'ui/slider/onValueChange',
    category: NodeCategory.UI,
    constructor: SliderValueChangeNode,
    label: '滑块值变化事件',
    description: '监听滑块值变化事件',
    icon: 'slider',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'range', 'change', 'event']
  });
}
