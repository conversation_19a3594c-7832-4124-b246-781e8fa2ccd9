/**
 * 视觉脚本UI节点
 * 提供UI交互相关的节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import type { NodeOptions } from '../nodes/Node';

/**
 * 按钮点击事件节点
 * 监听按钮点击事件
 */
export class ButtonClickNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的按钮元素'
    });
  }

  /**
   * 开始监听事件
   */
  public start(): void {
    const element = this.getInputValue('element') as HTMLElement;
    if (element) {
      element.addEventListener('click', () => {
        this.triggerEvent();
      });
    }
  }
}

/**
 * 创建按钮节点 (229)
 * 创建一个UI按钮
 */
export class CreateButtonNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按钮显示的文本',
      defaultValue: '按钮'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的按钮对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const button = document.createElement('button');
      button.textContent = text;
      button.style.position = 'absolute';
      button.style.left = '10px';
      button.style.top = '10px';
      button.style.zIndex = '1000';

      document.body.appendChild(button);

      this.setOutputValue('button', button);
      this.triggerFlow('flow');
      return button;
    } catch (error) {
      this.setOutputValue('button', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置按钮文本节点 (231)
 * 设置按钮显示的文本
 */
export class SetButtonTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置文本的按钮对象'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '新的按钮文本',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置文本'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const text = this.getInputValue('text') as string;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.textContent = text;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置按钮状态节点 (232)
 * 设置按钮是否可用
 */
export class SetButtonEnabledNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置状态的按钮对象'
    });

    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '按钮是否可用',
      defaultValue: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置状态'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const enabled = this.getInputValue('enabled') as boolean;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.disabled = !enabled;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建文本节点 (233)
 * 创建一个UI文本标签
 */
export class CreateTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '显示的文本内容',
      defaultValue: '文本'
    });

    this.addOutput({
      name: 'textElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的文本元素对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const textElement = document.createElement('div');
      textElement.textContent = text;
      textElement.style.position = 'absolute';
      textElement.style.left = '10px';
      textElement.style.top = '50px';
      textElement.style.zIndex = '1000';

      document.body.appendChild(textElement);

      this.setOutputValue('textElement', textElement);
      this.triggerFlow('flow');
      return textElement;
    } catch (error) {
      this.setOutputValue('textElement', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 输入框变化事件节点
 * 监听输入框内容变化事件
 */
export class InputChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '输入框的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', element.value);
        this.triggerEvent();
      });
    }
  }
}

/**
 * 滑块值变化事件节点
 * 监听滑块值变化事件
 */
export class SliderValueChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的滑块元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滑块的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', parseFloat(element.value));
        this.triggerEvent();
      });
    }
  }
}

/**
 * 注册UI节点
 * @param registry 节点注册表
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点 (229)
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个UI按钮',
    icon: 'button',
    color: '#EB2F96',
    tags: ['ui', 'button', 'create']
  });

  // 注册按钮点击事件节点 (230)
  registry.registerNodeType({
    type: 'ui/button/onClick',
    category: NodeCategory.UI,
    constructor: ButtonClickNode,
    label: '按钮点击',
    description: '监听按钮点击事件',
    icon: 'click',
    color: '#EB2F96',
    tags: ['ui', 'button', 'click', 'event']
  });

  // 注册设置按钮文本节点 (231)
  registry.registerNodeType({
    type: 'ui/button/setText',
    category: NodeCategory.UI,
    constructor: SetButtonTextNode,
    label: '设置按钮文本',
    description: '设置按钮显示的文本',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'button', 'text', 'set']
  });

  // 注册设置按钮状态节点 (232)
  registry.registerNodeType({
    type: 'ui/button/setEnabled',
    category: NodeCategory.UI,
    constructor: SetButtonEnabledNode,
    label: '设置按钮状态',
    description: '设置按钮是否可用',
    icon: 'toggle',
    color: '#EB2F96',
    tags: ['ui', 'button', 'enabled', 'set']
  });

  // 注册创建文本节点 (233)
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个UI文本标签',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'text', 'create']
  });

  // 注册输入框变化事件节点 (240)
  registry.registerNodeType({
    type: 'ui/input/onChange',
    category: NodeCategory.UI,
    constructor: InputChangeNode,
    label: '输入变化',
    description: '监听输入框内容变化事件',
    icon: 'edit',
    color: '#EB2F96',
    tags: ['ui', 'input', 'change', 'event']
  });

  // 注册滑块值变化事件节点
  registry.registerNodeType({
    type: 'ui/slider/onValueChange',
    category: NodeCategory.UI,
    constructor: SliderValueChangeNode,
    label: '滑块值变化事件',
    description: '监听滑块值变化事件',
    icon: 'slider',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'range', 'change', 'event']
  });
}
