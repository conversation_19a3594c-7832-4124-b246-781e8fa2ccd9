/**
 * 相机和UI节点测试
 * 测试新实现的相机控制和UI控件节点
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerRenderNodes } from '../presets/RenderNodes';
import { registerUINodes } from '../presets/UINodes';

/**
 * 相机和UI节点测试类
 */
export class CameraUINodesTest {
  private registry: NodeRegistry;

  constructor() {
    this.registry = new NodeRegistry();
    
    // 注册渲染节点（包含相机节点）
    registerRenderNodes(this.registry);
    
    // 注册UI节点
    registerUINodes(this.registry);
  }

  /**
   * 测试相机控制节点
   */
  public testCameraNodes(): void {
    console.log('=== 测试相机控制节点 ===');

    // 测试的相机节点类型
    const cameraNodes = [
      'camera/getPosition',    // 226
      'camera/lookAt',         // 227
      'camera/setFOV',         // 228
      'camera/setPosition'     // 225 (已存在)
    ];

    console.log('验证相机节点注册...');
    for (const nodeType of cameraNodes) {
      const nodeInfo = this.registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`相机节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }

    console.log('✓ 相机控制节点测试通过');
  }

  /**
   * 测试UI控件节点
   */
  public testUINodes(): void {
    console.log('=== 测试UI控件节点 ===');

    // 测试的UI节点类型
    const uiNodes = [
      'ui/button/create',      // 229
      'ui/button/onClick',     // 230
      'ui/button/setText',     // 231
      'ui/button/setEnabled',  // 232
      'ui/text/create',        // 233
      'ui/text/setText',       // 234
      'ui/text/setColor',      // 235
      'ui/text/setSize',       // 236
      'ui/input/create',       // 237
      'ui/input/getValue',     // 238
      'ui/input/setValue',     // 239
      'ui/input/onChange'      // 240
    ];

    console.log('验证UI节点注册...');
    for (const nodeType of uiNodes) {
      const nodeInfo = this.registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`UI节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }

    console.log('✓ UI控件节点测试通过');
  }

  /**
   * 测试节点分类
   */
  public testNodeCategories(): void {
    console.log('=== 测试节点分类 ===');

    // 测试相机分类
    const cameraNodes = this.registry.getNodeTypesByCategory('camera');
    console.log(`相机分类节点数量: ${cameraNodes.length}`);
    
    // 测试UI分类
    const uiNodes = this.registry.getNodeTypesByCategory('ui');
    console.log(`UI分类节点数量: ${uiNodes.length}`);

    // 测试渲染分类
    const renderNodes = this.registry.getNodeTypesByCategory('render');
    console.log(`渲染分类节点数量: ${renderNodes.length}`);

    console.log('✓ 节点分类测试通过');
  }

  /**
   * 测试节点搜索
   */
  public testNodeSearch(): void {
    console.log('=== 测试节点搜索 ===');

    // 搜索相机相关节点
    const cameraSearchResults = this.registry.searchNodeTypes('相机');
    console.log('搜索"相机"的结果数量:', cameraSearchResults.length);

    // 搜索按钮相关节点
    const buttonSearchResults = this.registry.searchNodeTypes('按钮');
    console.log('搜索"按钮"的结果数量:', buttonSearchResults.length);

    // 搜索文本相关节点
    const textSearchResults = this.registry.searchNodeTypes('文本');
    console.log('搜索"文本"的结果数量:', textSearchResults.length);

    // 搜索输入相关节点
    const inputSearchResults = this.registry.searchNodeTypes('输入');
    console.log('搜索"输入"的结果数量:', inputSearchResults.length);

    console.log('✓ 节点搜索测试通过');
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    console.log('开始测试相机和UI节点...\n');

    try {
      this.testCameraNodes();
      console.log('');
      
      this.testUINodes();
      console.log('');
      
      this.testNodeCategories();
      console.log('');
      
      this.testNodeSearch();
      console.log('');

      console.log('🎉 所有测试通过！');
      console.log('已成功实现15个新节点：');
      console.log('- 相机控制节点：3个 (226-228)');
      console.log('- UI控件节点：12个 (229-240)');
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      throw error;
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new CameraUINodesTest();
  test.runAllTests();
}
