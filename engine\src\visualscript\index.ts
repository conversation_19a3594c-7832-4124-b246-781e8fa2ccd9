/**
 * 视觉脚本系统模块
 * 导出所有视觉脚本系统相关的类和接口
 */

// 核心系统
export * from './VisualScriptSystem';
export * from './VisualScriptEngine';
export * from './VisualScriptComponent';

// 节点系统
export * from './nodes/Node';
export * from './nodes/FlowNode';
export * from './nodes/EventNode';
export * from './nodes/FunctionNode';
export * from './nodes/AsyncNode';
export * from './nodes/NodeRegistry';

// 图形系统
export * from './graph/Graph';
export * from './graph/GraphJSON';

// 执行系统
export * from './execution/Fiber';
export * from './execution/ExecutionContext';

// 事件系统
export * from './events/CustomEvent';

// 值类型系统
export * from './values/ValueTypeRegistry';
export * from './values/Variable';

// 工具类
// 注意：Logger 和 Assert 工具类文件不存在，已移除导出

// 编辑器集成
// 注意：NodeEditor 和 GraphEditor 文件不存在，已移除导出

// 预设节点
export * from './presets/CoreNodes';
export * from './presets/MathNodes';
export * from './presets/EntityNodes';
export * from './presets/PhysicsNodes';
export * from './presets/AnimationNodes';
export * from './presets/InputNodes';
export * from './presets/AudioNodes';
export * from './presets/NetworkNodes';
export * from './presets/TransformNodes';
export * from './presets/SceneNodes';

// 逻辑节点 - 显式导出以避免与CoreNodes冲突
export {
  ComparisonNode,
  LogicalOperationNode,
  ToggleNode,
  BranchNode as LogicBranchNode  // 重命名以避免冲突
} from './presets/LogicNodes';

// 时间节点 - 显式导出新实现的节点
export {
  GetCurrentTimeNode,
  GetDeltaTimeNode,
  DelayNode,
  TimerNode,
  StopwatchNode,
  FormatTimeNode
} from './presets/TimeNodes';

// 动画节点 - 显式导出新实现的节点
export {
  PlayAnimationNode,
  StopAnimationNode,
  PauseAnimationNode,
  ResumeAnimationNode,
  SetAnimationSpeedNode,
  GetAnimationStateNode,
  SetAnimationTimeNode,
  CrossFadeAnimationNode
} from './presets/AnimationNodes';

// 输入节点 - 显式导出新实现的节点
export {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode,
  IsKeyDownNode,
  IsKeyUpNode,
  // 新增输入处理节点 (152-165)
  OnKeyPressNode,
  GetMousePositionNode,
  IsMouseButtonDownNode,
  OnMouseClickNode,
  OnMouseMoveNode,
  OnMouseWheelNode,
  GetTouchCountNode,
  GetTouchPositionNode,
  OnTouchStartNode,
  OnTouchEndNode,
  OnTouchMoveNode,
  IsGamepadConnectedNode,
  GetGamepadButtonStateNode,
  GetGamepadAxisValueNode
} from './presets/InputNodes';

// 物理节点 - 显式导出新实现的节点 (196-207)
export {
  RaycastNode,
  ApplyForceNode,
  ApplyImpulseNode,
  SetVelocityNode,
  GetVelocityNode,
  SetMassNode,
  GetMassNode,
  OnCollisionEnterNode,
  OnCollisionExitNode,
  OnTriggerEnterNode,
  OnTriggerExitNode,
  SetGravityNode
} from './presets/PhysicsNodes';

// 音频节点 - 显式导出新实现的节点 (208-210)
export {
  PlayAudioNode,
  StopAudioNode
} from './presets/AudioNodes';
