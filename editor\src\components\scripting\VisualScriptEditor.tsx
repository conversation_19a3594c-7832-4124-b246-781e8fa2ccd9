/**
 * 可视化脚本编辑器组件
 * 提供节点式的可视化脚本编辑功能
 */
import React, { useState, useRef, useCallback } from 'react';
import {
  Button,
  Space,
  Tooltip,
  message,
  Drawer,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  PlusOutlined,
  SettingOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import NodeSearch from '../visualscript/NodeSearch';

const { Title, Text } = Typography;

/**
 * 节点分类枚举
 */
enum NodeCategory {
  EVENTS = 'events',
  FLOW = 'flow',
  LOGIC = 'logic',
  MATH = 'math',
  STRING = 'string',
  DEBUG = 'debug',
  ENTITY = 'entity',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  INPUT = 'input',
  UI = 'ui',
  NETWORK = 'network',
  AI = 'ai',
  TIME = 'time',
  ARRAY = 'array',
  OBJECT = 'object',
  VARIABLE = 'variable',
  CONSTANT = 'constant',
  FUNCTION = 'function',
  CUSTOM = 'custom'
}

/**
 * 节点信息接口
 */
interface NodeInfo {
  type: string;
  label: string;
  description: string;
  category: NodeCategory;
  icon: string;
  color: string;
  tags: string[];
}

/**
 * 可视化脚本数据接口
 */
interface VisualScriptData {
  nodes: any[];
  connections: any[];
  variables: any[];
  customEvents: any[];
}

/**
 * 可视化脚本编辑器属性
 */
interface VisualScriptEditorProps {
  /** 脚本数据 */
  value?: VisualScriptData;
  /** 是否只读 */
  readOnly?: boolean;
  /** 高度 */
  height?: string | number;
  /** 内容变化回调 */
  onChange?: (value: VisualScriptData) => void;
  /** 执行状态变化回调 */
  onExecutionStateChange?: (state: string) => void;
}

/**
 * 可视化脚本编辑器组件
 */
const VisualScriptEditor: React.FC<VisualScriptEditorProps> = ({
  value,
  readOnly = false,
  height = '500px',
  onChange,
  onExecutionStateChange
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 状态管理
  const [isExecuting, setIsExecuting] = useState(false);
  const [showNodeSearch, setShowNodeSearch] = useState(false);
  const [favoriteNodes, setFavoriteNodes] = useState<string[]>([]);
  const [recentNodes, setRecentNodes] = useState<string[]>([]);

  // 默认脚本数据
  const defaultScriptData: VisualScriptData = {
    nodes: [],
    connections: [],
    variables: [],
    customEvents: []
  };

  const scriptData = value || defaultScriptData;

  // 示例节点数据
  const availableNodes: NodeInfo[] = [
    // 现有节点
    {
      type: 'core/events/onStart',
      label: t('节点.开始事件'),
      description: t('节点.开始事件描述'),
      category: NodeCategory.EVENTS,
      icon: 'play',
      color: '#52c41a',
      tags: ['事件', '生命周期']
    },
    {
      type: 'core/events/onUpdate',
      label: t('节点.更新事件'),
      description: t('节点.更新事件描述'),
      category: NodeCategory.EVENTS,
      icon: 'sync',
      color: '#1890ff',
      tags: ['事件', '生命周期']
    },
    {
      type: 'core/debug/print',
      label: t('节点.打印'),
      description: t('节点.打印描述'),
      category: NodeCategory.DEBUG,
      icon: 'console',
      color: '#722ed1',
      tags: ['调试', '输出']
    },
    {
      type: 'core/math/add',
      label: t('节点.加法'),
      description: t('节点.加法描述'),
      category: NodeCategory.MATH,
      icon: 'plus',
      color: '#fa8c16',
      tags: ['数学', '运算']
    },

    // 音频控制节点 (211-217)
    {
      type: 'audio/playSound',
      label: '播放音效',
      description: '播放指定的音效文件',
      category: NodeCategory.AUDIO,
      icon: 'play-sound',
      color: '#FF9800',
      tags: ['音频', '播放']
    },
    {
      type: 'audio/resumeSound',
      label: '恢复音效',
      description: '恢复音效播放',
      category: NodeCategory.AUDIO,
      icon: 'resume-sound',
      color: '#FF9800',
      tags: ['音频', '恢复']
    },
    {
      type: 'audio/setVolume',
      label: '设置音量',
      description: '设置音频的音量',
      category: NodeCategory.AUDIO,
      icon: 'volume',
      color: '#FF9800',
      tags: ['音频', '音量']
    },
    {
      type: 'audio/getVolume',
      label: '获取音量',
      description: '获取当前音量',
      category: NodeCategory.AUDIO,
      icon: 'volume',
      color: '#FF9800',
      tags: ['音频', '音量']
    },
    {
      type: 'audio/setPitch',
      label: '设置音调',
      description: '设置音频的音调',
      category: NodeCategory.AUDIO,
      icon: 'pitch',
      color: '#FF9800',
      tags: ['音频', '音调']
    },
    {
      type: 'audio/setLoop',
      label: '设置循环',
      description: '设置音频是否循环播放',
      category: NodeCategory.AUDIO,
      icon: 'loop',
      color: '#FF9800',
      tags: ['音频', '循环']
    },
    {
      type: 'audio/play3DSound',
      label: '播放3D音效',
      description: '在3D空间中播放定位音效',
      category: NodeCategory.AUDIO,
      icon: '3d-sound',
      color: '#FF9800',
      tags: ['音频', '3D', '空间']
    },
    {
      type: 'audio/setListenerPosition',
      label: '设置听者位置',
      description: '设置3D音频听者位置',
      category: NodeCategory.AUDIO,
      icon: 'listener',
      color: '#FF9800',
      tags: ['音频', '3D', '听者']
    },

    // 渲染控制节点 (218-224)
    {
      type: 'render/setMaterial',
      label: '设置材质',
      description: '设置物体的渲染材质',
      category: NodeCategory.RENDER,
      icon: 'material',
      color: '#4CAF50',
      tags: ['渲染', '材质']
    },
    {
      type: 'render/getMaterial',
      label: '获取材质',
      description: '获取物体的当前材质',
      category: NodeCategory.RENDER,
      icon: 'material',
      color: '#4CAF50',
      tags: ['渲染', '材质']
    },
    {
      type: 'render/setColor',
      label: '设置颜色',
      description: '设置物体的颜色',
      category: NodeCategory.RENDER,
      icon: 'color',
      color: '#4CAF50',
      tags: ['渲染', '颜色']
    },
    {
      type: 'render/getColor',
      label: '获取颜色',
      description: '获取物体的当前颜色',
      category: NodeCategory.RENDER,
      icon: 'color',
      color: '#4CAF50',
      tags: ['渲染', '颜色']
    },
    {
      type: 'render/setTexture',
      label: '设置纹理',
      description: '设置材质的纹理',
      category: NodeCategory.RENDER,
      icon: 'texture',
      color: '#4CAF50',
      tags: ['渲染', '纹理']
    },
    {
      type: 'render/setVisible',
      label: '设置可见性',
      description: '设置物体是否可见',
      category: NodeCategory.RENDER,
      icon: 'visibility',
      color: '#4CAF50',
      tags: ['渲染', '可见性']
    },
    {
      type: 'render/isVisible',
      label: '获取可见性',
      description: '检查物体是否可见',
      category: NodeCategory.RENDER,
      icon: 'visibility',
      color: '#4CAF50',
      tags: ['渲染', '可见性']
    },

    // 相机控制节点 (225)
    {
      type: 'camera/setPosition',
      label: '设置相机位置',
      description: '设置相机的位置',
      category: NodeCategory.CAMERA,
      icon: 'camera',
      color: '#2196F3',
      tags: ['相机', '位置']
    },
    // 新增物理节点 (196-207)
    {
      type: 'physics/raycast',
      label: '射线检测',
      description: '执行物理射线检测',
      category: NodeCategory.PHYSICS,
      icon: 'ray',
      color: '#E91E63',
      tags: ['物理', '射线', '碰撞']
    },
    {
      type: 'physics/applyForce',
      label: '应用力',
      description: '向物理体应用力',
      category: NodeCategory.PHYSICS,
      icon: 'force',
      color: '#E91E63',
      tags: ['物理', '力', '动力学']
    },
    {
      type: 'physics/applyImpulse',
      label: '应用冲量',
      description: '向物理体应用瞬时冲量',
      category: NodeCategory.PHYSICS,
      icon: 'impulse',
      color: '#E91E63',
      tags: ['物理', '冲量', '动力学']
    },
    {
      type: 'physics/setVelocity',
      label: '设置速度',
      description: '设置物理体的速度',
      category: NodeCategory.PHYSICS,
      icon: 'velocity',
      color: '#E91E63',
      tags: ['物理', '速度', '动力学']
    },
    {
      type: 'physics/getVelocity',
      label: '获取速度',
      description: '获取物理体的当前速度',
      category: NodeCategory.PHYSICS,
      icon: 'velocity',
      color: '#E91E63',
      tags: ['物理', '速度', '查询']
    },
    {
      type: 'physics/setMass',
      label: '设置质量',
      description: '设置物理体的质量',
      category: NodeCategory.PHYSICS,
      icon: 'mass',
      color: '#E91E63',
      tags: ['物理', '质量', '属性']
    },
    {
      type: 'physics/getMass',
      label: '获取质量',
      description: '获取物理体的质量',
      category: NodeCategory.PHYSICS,
      icon: 'mass',
      color: '#E91E63',
      tags: ['物理', '质量', '查询']
    },
    {
      type: 'physics/onCollisionEnter',
      label: '碰撞开始',
      description: '监听碰撞开始事件',
      category: NodeCategory.PHYSICS,
      icon: 'collision-enter',
      color: '#E91E63',
      tags: ['物理', '碰撞', '事件']
    },
    {
      type: 'physics/onCollisionExit',
      label: '碰撞结束',
      description: '监听碰撞结束事件',
      category: NodeCategory.PHYSICS,
      icon: 'collision-exit',
      color: '#E91E63',
      tags: ['物理', '碰撞', '事件']
    },
    {
      type: 'physics/onTriggerEnter',
      label: '触发器进入',
      description: '监听触发器进入事件',
      category: NodeCategory.PHYSICS,
      icon: 'trigger-enter',
      color: '#E91E63',
      tags: ['物理', '触发器', '事件']
    },
    {
      type: 'physics/onTriggerExit',
      label: '触发器退出',
      description: '监听触发器退出事件',
      category: NodeCategory.PHYSICS,
      icon: 'trigger-exit',
      color: '#E91E63',
      tags: ['物理', '触发器', '事件']
    },
    {
      type: 'physics/setGravity',
      label: '设置重力',
      description: '设置物理世界的重力',
      category: NodeCategory.PHYSICS,
      icon: 'gravity',
      color: '#E91E63',
      tags: ['物理', '重力', '世界']
    },
    // 新增音频节点 (208-210)
    {
      type: 'audio/playSound',
      label: '播放音效',
      description: '播放指定的音效文件',
      category: NodeCategory.AUDIO,
      icon: 'play-sound',
      color: '#FF9800',
      tags: ['音频', '音效', '播放']
    },
    {
      type: 'audio/stopSound',
      label: '停止音效',
      description: '停止播放音效',
      category: NodeCategory.AUDIO,
      icon: 'stop-sound',
      color: '#FF9800',
      tags: ['音频', '音效', '停止']
    },
    {
      type: 'audio/pauseSound',
      label: '暂停音效',
      description: '暂停音效播放',
      category: NodeCategory.AUDIO,
      icon: 'pause-sound',
      color: '#FF9800',
      tags: ['音频', '音效', '暂停']
    },
    // 新增实体操作节点 (166-180)
    {
      type: 'entity/create',
      label: '创建实体',
      description: '创建一个新的实体',
      category: NodeCategory.ENTITY,
      icon: 'entity',
      color: '#4CAF50',
      tags: ['实体', '创建']
    },
    {
      type: 'entity/destroy',
      label: '销毁实体',
      description: '销毁指定的实体',
      category: NodeCategory.ENTITY,
      icon: 'entity',
      color: '#F44336',
      tags: ['实体', '销毁']
    },
    {
      type: 'entity/get',
      label: '获取实体',
      description: '根据ID或名称获取实体',
      category: NodeCategory.ENTITY,
      icon: 'entity',
      color: '#4CAF50',
      tags: ['实体', '获取']
    },
    {
      type: 'entity/getName',
      label: '获取实体名称',
      description: '获取实体的名称',
      category: NodeCategory.ENTITY,
      icon: 'text',
      color: '#2196F3',
      tags: ['实体', '名称', '获取']
    },
    {
      type: 'entity/setName',
      label: '设置实体名称',
      description: '设置实体的名称',
      category: NodeCategory.ENTITY,
      icon: 'text',
      color: '#2196F3',
      tags: ['实体', '名称', '设置']
    },
    {
      type: 'entity/getTag',
      label: '获取实体标签',
      description: '获取实体的标签',
      category: NodeCategory.ENTITY,
      icon: 'tag',
      color: '#FF9800',
      tags: ['实体', '标签', '获取']
    },
    {
      type: 'entity/setTag',
      label: '设置实体标签',
      description: '设置实体的标签',
      category: NodeCategory.ENTITY,
      icon: 'tag',
      color: '#FF9800',
      tags: ['实体', '标签', '设置']
    },
    {
      type: 'entity/isActive',
      label: '实体激活状态',
      description: '检查实体是否处于激活状态',
      category: NodeCategory.ENTITY,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['实体', '激活', '状态']
    },
    {
      type: 'entity/setActive',
      label: '设置激活状态',
      description: '设置实体的激活状态',
      category: NodeCategory.ENTITY,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['实体', '激活', '设置']
    },
    {
      type: 'entity/getParent',
      label: '获取父实体',
      description: '获取实体的父实体',
      category: NodeCategory.ENTITY,
      icon: 'hierarchy',
      color: '#607D8B',
      tags: ['实体', '父级', '层级']
    },
    {
      type: 'entity/setParent',
      label: '设置父实体',
      description: '设置实体的父实体',
      category: NodeCategory.ENTITY,
      icon: 'hierarchy',
      color: '#607D8B',
      tags: ['实体', '父级', '层级']
    },
    {
      type: 'entity/getChildren',
      label: '获取子实体',
      description: '获取实体的所有子实体',
      category: NodeCategory.ENTITY,
      icon: 'hierarchy',
      color: '#607D8B',
      tags: ['实体', '子级', '层级']
    },
    {
      type: 'component/add',
      label: '添加组件',
      description: '向实体添加指定类型的组件',
      category: NodeCategory.ENTITY,
      icon: 'addComponent',
      color: '#4CAF50',
      tags: ['组件', '添加']
    },
    {
      type: 'component/remove',
      label: '移除组件',
      description: '从实体移除指定类型的组件',
      category: NodeCategory.ENTITY,
      icon: 'removeComponent',
      color: '#F44336',
      tags: ['组件', '移除']
    },
    {
      type: 'component/get',
      label: '获取组件',
      description: '获取实体上的指定组件',
      category: NodeCategory.ENTITY,
      icon: 'component',
      color: '#4CAF50',
      tags: ['组件', '获取']
    },
    {
      type: 'core/flow/delay',
      label: t('节点.延迟'),
      description: t('节点.延迟描述'),
      category: NodeCategory.FLOW,
      icon: 'clock',
      color: '#eb2f96',
      tags: ['流程', '时间']
    },

    // 新增流程控制节点
    {
      type: 'core/flow/branch',
      label: '分支',
      description: '根据条件选择执行路径',
      category: NodeCategory.FLOW,
      icon: 'fork',
      color: '#FF9800',
      tags: ['流程', '控制', '分支']
    },
    {
      type: 'core/flow/sequence',
      label: '序列',
      description: '按顺序执行多个流程',
      category: NodeCategory.FLOW,
      icon: 'ordered-list',
      color: '#9C27B0',
      tags: ['流程', '控制', '序列']
    },
    {
      type: 'core/flow/loop',
      label: '循环',
      description: '重复执行指定次数',
      category: NodeCategory.FLOW,
      icon: 'reload',
      color: '#722ED1',
      tags: ['流程', '控制', '循环']
    },
    {
      type: 'core/flow/while',
      label: '条件循环',
      description: '当条件为真时重复执行',
      category: NodeCategory.FLOW,
      icon: 'sync',
      color: '#722ED1',
      tags: ['流程', '控制', '条件循环']
    },
    {
      type: 'core/flow/forEach',
      label: '遍历循环',
      description: '遍历数组或集合中的每个元素',
      category: NodeCategory.FLOW,
      icon: 'unordered-list',
      color: '#722ED1',
      tags: ['流程', '控制', '遍历']
    },

    // 新增数学运算节点
    {
      type: 'math/basic/subtract',
      label: '减法',
      description: '计算两个数的差',
      category: NodeCategory.MATH,
      icon: 'minus',
      color: '#2196F3',
      tags: ['数学', '基础', '运算']
    },
    {
      type: 'math/basic/multiply',
      label: '乘法',
      description: '计算两个数的积',
      category: NodeCategory.MATH,
      icon: 'close',
      color: '#2196F3',
      tags: ['数学', '基础', '运算']
    },
    {
      type: 'math/basic/divide',
      label: '除法',
      description: '计算两个数的商',
      category: NodeCategory.MATH,
      icon: 'line',
      color: '#2196F3',
      tags: ['数学', '基础', '运算']
    },
    {
      type: 'math/basic/modulo',
      label: '取模',
      description: '计算两个数的模',
      category: NodeCategory.MATH,
      icon: 'percentage',
      color: '#2196F3',
      tags: ['数学', '基础', '运算']
    },
    {
      type: 'math/advanced/power',
      label: '幂运算',
      description: '计算一个数的幂',
      category: NodeCategory.MATH,
      icon: 'rise',
      color: '#2196F3',
      tags: ['数学', '高级', '运算']
    },
    {
      type: 'math/advanced/sqrt',
      label: '平方根',
      description: '计算一个数的平方根',
      category: NodeCategory.MATH,
      icon: 'radical',
      color: '#2196F3',
      tags: ['数学', '高级', '运算']
    },
    {
      type: 'math/trigonometric/sin',
      label: '正弦',
      description: '计算正弦值',
      category: NodeCategory.MATH,
      icon: 'function',
      color: '#2196F3',
      tags: ['数学', '三角函数', '正弦']
    },
    {
      type: 'math/trigonometric/cos',
      label: '余弦',
      description: '计算余弦值',
      category: NodeCategory.MATH,
      icon: 'function',
      color: '#2196F3',
      tags: ['数学', '三角函数', '余弦']
    },
    {
      type: 'math/trigonometric/tan',
      label: '正切',
      description: '计算正切值',
      category: NodeCategory.MATH,
      icon: 'function',
      color: '#2196F3',
      tags: ['数学', '三角函数', '正切']
    },
    {
      type: 'math/advanced/abs',
      label: '绝对值',
      description: '计算数的绝对值',
      category: NodeCategory.MATH,
      icon: 'number',
      color: '#2196F3',
      tags: ['数学', '高级', '绝对值']
    },
    {
      type: 'math/advanced/min',
      label: '最小值',
      description: '获取多个数中的最小值',
      category: NodeCategory.MATH,
      icon: 'down',
      color: '#2196F3',
      tags: ['数学', '高级', '最小值']
    },
    {
      type: 'math/advanced/max',
      label: '最大值',
      description: '获取多个数中的最大值',
      category: NodeCategory.MATH,
      icon: 'up',
      color: '#2196F3',
      tags: ['数学', '高级', '最大值']
    },
    {
      type: 'math/advanced/clamp',
      label: '限制范围',
      description: '将数值限制在指定范围内',
      category: NodeCategory.MATH,
      icon: 'border',
      color: '#2196F3',
      tags: ['数学', '高级', '限制']
    },

    // 新增逻辑操作节点
    {
      type: 'logic/flow/branch',
      label: '逻辑分支',
      description: '根据条件选择执行路径',
      category: NodeCategory.LOGIC,
      icon: 'fork',
      color: '#FF9800',
      tags: ['逻辑', '流程', '分支']
    },
    {
      type: 'logic/comparison/equal',
      label: '相等',
      description: '比较两个值是否相等',
      category: NodeCategory.LOGIC,
      icon: 'equal',
      color: '#FF9800',
      tags: ['逻辑', '比较', '相等']
    },
    {
      type: 'logic/comparison/notEqual',
      label: '不相等',
      description: '比较两个值是否不相等',
      category: NodeCategory.LOGIC,
      icon: 'not-equal',
      color: '#FF9800',
      tags: ['逻辑', '比较', '不相等']
    },
    {
      type: 'logic/comparison/greater',
      label: '大于',
      description: '比较第一个值是否大于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'right',
      color: '#FF9800',
      tags: ['逻辑', '比较', '大于']
    },
    {
      type: 'logic/comparison/greaterEqual',
      label: '大于等于',
      description: '比较第一个值是否大于等于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'right',
      color: '#FF9800',
      tags: ['逻辑', '比较', '大于等于']
    },
    {
      type: 'logic/comparison/less',
      label: '小于',
      description: '比较第一个值是否小于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'left',
      color: '#FF9800',
      tags: ['逻辑', '比较', '小于']
    },
    {
      type: 'logic/comparison/lessEqual',
      label: '小于等于',
      description: '比较第一个值是否小于等于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'left',
      color: '#FF9800',
      tags: ['逻辑', '比较', '小于等于']
    },
    {
      type: 'logic/operation/and',
      label: '与运算',
      description: '执行逻辑与运算',
      category: NodeCategory.LOGIC,
      icon: 'intersection',
      color: '#FF9800',
      tags: ['逻辑', '运算', '与']
    },
    {
      type: 'logic/operation/or',
      label: '或运算',
      description: '执行逻辑或运算',
      category: NodeCategory.LOGIC,
      icon: 'union',
      color: '#FF9800',
      tags: ['逻辑', '运算', '或']
    },
    {
      type: 'logic/operation/not',
      label: '非运算',
      description: '执行逻辑非运算',
      category: NodeCategory.LOGIC,
      icon: 'exclamation',
      color: '#FF9800',
      tags: ['逻辑', '运算', '非']
    },
    {
      type: 'logic/flow/toggle',
      label: '开关',
      description: '在两个状态之间切换',
      category: NodeCategory.LOGIC,
      icon: 'swap',
      color: '#FF9800',
      tags: ['逻辑', '流程', '开关']
    },

    // 新增实体操作节点
    {
      type: 'entity/get',
      label: '获取实体',
      description: '根据ID获取实体',
      category: NodeCategory.ENTITY,
      icon: 'block',
      color: '#4CAF50',
      tags: ['实体', '获取']
    },
    {
      type: 'entity/component/get',
      label: '获取组件',
      description: '获取实体上的组件',
      category: NodeCategory.ENTITY,
      icon: 'component',
      color: '#4CAF50',
      tags: ['实体', '组件', '获取']
    },

    // 新增物理模拟节点
    {
      type: 'physics/raycast',
      label: '射线检测',
      description: '执行物理射线检测',
      category: NodeCategory.PHYSICS,
      icon: 'aim',
      color: '#E91E63',
      tags: ['物理', '射线', '检测']
    },
    {
      type: 'physics/applyForce',
      label: '应用力',
      description: '向物理体应用力',
      category: NodeCategory.PHYSICS,
      icon: 'arrow-up',
      color: '#E91E63',
      tags: ['物理', '力', '动力学']
    },
    {
      type: 'physics/softbody/createCloth',
      label: '创建布料',
      description: '创建布料软体',
      category: NodeCategory.PHYSICS,
      icon: 'skin',
      color: '#9C27B0',
      tags: ['物理', '软体', '布料']
    },
    {
      type: 'physics/softbody/createRope',
      label: '创建绳索',
      description: '创建绳索软体',
      category: NodeCategory.PHYSICS,
      icon: 'link',
      color: '#9C27B0',
      tags: ['物理', '软体', '绳索']
    },

    // 新增网络通信节点
    {
      type: 'network/connectToServer',
      label: '连接到服务器',
      description: '连接到网络服务器',
      category: NodeCategory.NETWORK,
      icon: 'api',
      color: '#00BCD4',
      tags: ['网络', '连接', '服务器']
    },
    {
      type: 'network/sendMessage',
      label: '发送网络消息',
      description: '向其他用户发送网络消息',
      category: NodeCategory.NETWORK,
      icon: 'send',
      color: '#00BCD4',
      tags: ['网络', '消息', '发送']
    },
    {
      type: 'network/events/onMessage',
      label: '接收网络消息',
      description: '当接收到网络消息时触发',
      category: NodeCategory.NETWORK,
      icon: 'message',
      color: '#00BCD4',
      tags: ['网络', '消息', '接收', '事件']
    },

    // 新增AI功能节点
    {
      type: 'ai/animation/generateBodyAnimation',
      label: '生成身体动画',
      description: '使用AI生成身体动画',
      category: NodeCategory.AI,
      icon: 'robot',
      color: '#FF5722',
      tags: ['AI', '动画', '身体', '生成']
    },
    {
      type: 'ai/animation/generateFacialAnimation',
      label: '生成面部动画',
      description: '使用AI生成面部动画',
      category: NodeCategory.AI,
      icon: 'smile',
      color: '#FF5722',
      tags: ['AI', '动画', '面部', '生成']
    },
    {
      type: 'ai/nlp/classifyText',
      label: '文本分类',
      description: '使用AI对文本进行分类',
      category: NodeCategory.AI,
      icon: 'file-text',
      color: '#FF5722',
      tags: ['AI', 'NLP', '文本', '分类']
    },
    {
      type: 'ai/nlp/recognizeEntities',
      label: '命名实体识别',
      description: '识别文本中的命名实体',
      category: NodeCategory.AI,
      icon: 'tag',
      color: '#FF5722',
      tags: ['AI', 'NLP', '实体', '识别']
    },

    // 新增调试工具节点
    {
      type: 'debug/breakpoint',
      label: '断点',
      description: '设置执行断点',
      category: NodeCategory.DEBUG,
      icon: 'pause-circle',
      color: '#722ed1',
      tags: ['调试', '断点', '暂停']
    },
    {
      type: 'debug/log',
      label: '日志',
      description: '输出调试日志',
      category: NodeCategory.DEBUG,
      icon: 'file-text',
      color: '#722ed1',
      tags: ['调试', '日志', '输出']
    },
    {
      type: 'debug/performanceTimer',
      label: '性能计时',
      description: '测量代码执行时间',
      category: NodeCategory.DEBUG,
      icon: 'clock-circle',
      color: '#722ed1',
      tags: ['调试', '性能', '计时']
    },
    {
      type: 'debug/variableWatch',
      label: '变量监视',
      description: '监视变量值的变化',
      category: NodeCategory.DEBUG,
      icon: 'eye',
      color: '#722ed1',
      tags: ['调试', '变量', '监视']
    },
    {
      type: 'debug/assert',
      label: '断言',
      description: '验证条件是否为真',
      category: NodeCategory.DEBUG,
      icon: 'check-circle',
      color: '#722ed1',
      tags: ['调试', '断言', '验证']
    },

    // 新增时间控制节点
    {
      type: 'GetTime',
      label: '获取时间',
      description: '获取当前系统时间',
      category: NodeCategory.TIME,
      icon: 'clock-circle',
      color: '#13C2C2',
      tags: ['时间', '获取', '系统']
    },
    {
      type: 'Delay',
      label: '延迟',
      description: '延迟指定时间后执行',
      category: NodeCategory.TIME,
      icon: 'pause',
      color: '#13C2C2',
      tags: ['时间', '延迟', '等待']
    },
    {
      type: 'Timer',
      label: '计时器',
      description: '创建一个计时器',
      category: NodeCategory.TIME,
      icon: 'hourglass',
      color: '#13C2C2',
      tags: ['时间', '计时器', '定时']
    },

    // 新增动画系统节点
    {
      type: 'PlayAnimation',
      label: '播放动画',
      description: '播放指定的动画',
      category: NodeCategory.ANIMATION,
      icon: 'play-circle',
      color: '#52C41A',
      tags: ['动画', '播放', '控制']
    },
    {
      type: 'StopAnimation',
      label: '停止动画',
      description: '停止当前播放的动画',
      category: NodeCategory.ANIMATION,
      icon: 'stop',
      color: '#52C41A',
      tags: ['动画', '停止', '控制']
    },
    {
      type: 'SetAnimationSpeed',
      label: '设置动画速度',
      description: '设置动画播放速度',
      category: NodeCategory.ANIMATION,
      icon: 'fast-forward',
      color: '#52C41A',
      tags: ['动画', '速度', '控制']
    },
    {
      type: 'GetAnimationState',
      label: '获取动画状态',
      description: '获取当前动画的播放状态',
      category: NodeCategory.ANIMATION,
      icon: 'info-circle',
      color: '#52C41A',
      tags: ['动画', '状态', '查询']
    },

    // 新增输入处理节点
    {
      type: 'KeyboardInput',
      label: '键盘输入',
      description: '处理键盘输入事件',
      category: NodeCategory.INPUT,
      icon: 'keyboard',
      color: '#1890FF',
      tags: ['输入', '键盘', '事件']
    },
    {
      type: 'MouseInput',
      label: '鼠标输入',
      description: '处理鼠标输入事件',
      category: NodeCategory.INPUT,
      icon: 'mouse',
      color: '#1890FF',
      tags: ['输入', '鼠标', '事件']
    },
    {
      type: 'TouchInput',
      label: '触摸输入',
      description: '处理触摸输入事件',
      category: NodeCategory.INPUT,
      icon: 'mobile',
      color: '#1890FF',
      tags: ['输入', '触摸', '事件']
    },
    {
      type: 'GamepadInput',
      label: '手柄输入',
      description: '处理游戏手柄输入事件',
      category: NodeCategory.INPUT,
      icon: 'gamepad',
      color: '#1890FF',
      tags: ['输入', '手柄', '游戏']
    },

    // 新增音频控制节点
    {
      type: 'PlayAudio',
      label: '播放音频',
      description: '播放指定的音频文件',
      category: NodeCategory.AUDIO,
      icon: 'sound',
      color: '#FA8C16',
      tags: ['音频', '播放', '声音']
    },
    {
      type: 'StopAudio',
      label: '停止音频',
      description: '停止播放音频',
      category: NodeCategory.AUDIO,
      icon: 'stop',
      color: '#FA8C16',
      tags: ['音频', '停止', '声音']
    },
    {
      type: 'SetVolume',
      label: '设置音量',
      description: '设置音频的音量大小',
      category: NodeCategory.AUDIO,
      icon: 'sound',
      color: '#FA8C16',
      tags: ['音频', '音量', '控制']
    },
    {
      type: 'AudioAnalyzer',
      label: '音频分析器',
      description: '分析音频频谱和波形',
      category: NodeCategory.AUDIO,
      icon: 'bar-chart',
      color: '#FA8C16',
      tags: ['音频', '分析', '频谱']
    },
    {
      type: 'Audio3D',
      label: '3D音频',
      description: '在3D空间中播放定位音频',
      category: NodeCategory.AUDIO,
      icon: 'environment',
      color: '#FA8C16',
      tags: ['音频', '3D', '空间']
    },

    // 新增UI交互节点
    {
      type: 'ui/button/onClick',
      label: '按钮点击事件',
      description: '监听按钮点击事件',
      category: NodeCategory.UI,
      icon: 'click',
      color: '#EB2F96',
      tags: ['UI', '按钮', '点击', '事件']
    },
    {
      type: 'ui/input/onChange',
      label: '输入框变化事件',
      description: '监听输入框内容变化事件',
      category: NodeCategory.UI,
      icon: 'edit',
      color: '#EB2F96',
      tags: ['UI', '输入框', '变化', '事件']
    },
    {
      type: 'ui/slider/onValueChange',
      label: '滑块值变化事件',
      description: '监听滑块值变化事件',
      category: NodeCategory.UI,
      icon: 'slider',
      color: '#EB2F96',
      tags: ['UI', '滑块', '值变化', '事件']
    },

    // 新增字符串操作节点
    {
      type: 'string/concat',
      label: '字符串连接',
      description: '连接多个字符串',
      category: NodeCategory.STRING,
      icon: 'link',
      color: '#52C41A',
      tags: ['字符串', '连接', '合并']
    },
    {
      type: 'string/split',
      label: '字符串分割',
      description: '按指定分隔符分割字符串',
      category: NodeCategory.STRING,
      icon: 'scissor',
      color: '#52C41A',
      tags: ['字符串', '分割', '数组']
    },

    // 新增数组操作节点
    {
      type: 'array/push',
      label: '数组添加元素',
      description: '向数组末尾添加元素',
      category: NodeCategory.ARRAY,
      icon: 'plus-circle',
      color: '#1890FF',
      tags: ['数组', '添加', '元素']
    },
    {
      type: 'array/pop',
      label: '数组移除元素',
      description: '移除并返回数组末尾元素',
      category: NodeCategory.ARRAY,
      icon: 'minus-circle',
      color: '#1890FF',
      tags: ['数组', '移除', '元素']
    },

    // 新增对象操作节点
    {
      type: 'object/getProperty',
      label: '获取对象属性',
      description: '获取对象的指定属性值',
      category: NodeCategory.OBJECT,
      icon: 'eye',
      color: '#722ED1',
      tags: ['对象', '属性', '获取']
    },
    {
      type: 'object/setProperty',
      label: '设置对象属性',
      description: '设置对象的指定属性值',
      category: NodeCategory.OBJECT,
      icon: 'edit',
      color: '#722ED1',
      tags: ['对象', '属性', '设置']
    },

    // 新增变量操作节点
    {
      type: 'variable/get',
      label: '获取变量',
      description: '获取指定名称的变量值',
      category: NodeCategory.VARIABLE,
      icon: 'download',
      color: '#FA8C16',
      tags: ['变量', '获取', '读取']
    },
    {
      type: 'variable/set',
      label: '设置变量',
      description: '设置指定名称的变量值',
      category: NodeCategory.VARIABLE,
      icon: 'upload',
      color: '#FA8C16',
      tags: ['变量', '设置', '写入']
    },
    {
      type: 'variable/increment',
      label: '变量自增',
      description: '将变量值增加指定数量',
      category: NodeCategory.VARIABLE,
      icon: 'plus-circle',
      color: '#FA8C16',
      tags: ['变量', '自增', '数学']
    },
    {
      type: 'variable/decrement',
      label: '变量自减',
      description: '将变量值减少指定数量',
      category: NodeCategory.VARIABLE,
      icon: 'minus-circle',
      color: '#FA8C16',
      tags: ['变量', '自减', '数学']
    },

    // 新增常量节点
    {
      type: 'constant/number',
      label: '数字常量',
      description: '输出指定的数字常量',
      category: NodeCategory.CONSTANT,
      icon: 'number',
      color: '#52C41A',
      tags: ['常量', '数字', '值']
    },
    {
      type: 'constant/string',
      label: '字符串常量',
      description: '输出指定的字符串常量',
      category: NodeCategory.CONSTANT,
      icon: 'font-size',
      color: '#52C41A',
      tags: ['常量', '字符串', '文本']
    },
    {
      type: 'constant/boolean',
      label: '布尔常量',
      description: '输出指定的布尔常量',
      category: NodeCategory.CONSTANT,
      icon: 'check-circle',
      color: '#52C41A',
      tags: ['常量', '布尔', '真假']
    },

    // 新增函数操作节点
    {
      type: 'function/call',
      label: '调用函数',
      description: '调用指定的函数',
      category: NodeCategory.FUNCTION,
      icon: 'function',
      color: '#13C2C2',
      tags: ['函数', '调用', '执行']
    },
    {
      type: 'function/return',
      label: '返回值',
      description: '从函数中返回值',
      category: NodeCategory.FUNCTION,
      icon: 'rollback',
      color: '#13C2C2',
      tags: ['函数', '返回', '退出']
    }
  ];

  // 处理脚本执行
  const handleExecute = useCallback(async () => {
    if (isExecuting) return;
    
    setIsExecuting(true);
    if (onExecutionStateChange) {
      onExecutionStateChange('running');
    }

    try {
      // 模拟脚本执行
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success(t('可视化脚本.执行成功'));
    } catch (error) {
      message.error(t('可视化脚本.执行失败'));
    } finally {
      setIsExecuting(false);
      if (onExecutionStateChange) {
        onExecutionStateChange('stopped');
      }
    }
  }, [isExecuting, onExecutionStateChange, t]);

  // 处理脚本停止
  const handleStop = useCallback(() => {
    setIsExecuting(false);
    if (onExecutionStateChange) {
      onExecutionStateChange('stopped');
    }
    message.info(t('可视化脚本.已停止'));
  }, [onExecutionStateChange, t]);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string) => {
    const newNode = {
      id: `node_${Date.now()}`,
      type: nodeType,
      position: { x: 100, y: 100 },
      data: {}
    };

    const newScriptData = {
      ...scriptData,
      nodes: [...scriptData.nodes, newNode]
    };

    if (onChange) {
      onChange(newScriptData);
    }

    // 更新最近使用的节点
    const updatedRecentNodes = [nodeType, ...recentNodes.filter(n => n !== nodeType)].slice(0, 10);
    setRecentNodes(updatedRecentNodes);

    setShowNodeSearch(false);
    message.success(t('可视化脚本.节点已添加'));
  }, [scriptData, onChange, recentNodes, t]);

  // 处理收藏节点切换
  const handleToggleFavorite = useCallback((nodeType: string) => {
    const isFavorite = favoriteNodes.includes(nodeType);
    const updatedFavorites = isFavorite
      ? favoriteNodes.filter(n => n !== nodeType)
      : [...favoriteNodes, nodeType];
    
    setFavoriteNodes(updatedFavorites);
    message.success(isFavorite ? t('可视化脚本.已取消收藏') : t('可视化脚本.已收藏'));
  }, [favoriteNodes, t]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onChange) {
      onChange(scriptData);
    }
    message.success(t('可视化脚本.保存成功'));
  }, [scriptData, onChange, t]);

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '8px 16px',
      borderBottom: '1px solid #f0f0f0',
      backgroundColor: '#fafafa'
    }}>
      <Space>
        <Tooltip title={t('可视化脚本.执行脚本')}>
          <Button
            type="primary"
            icon={isExecuting ? <LoadingOutlined /> : <PlayCircleOutlined />}
            onClick={handleExecute}
            disabled={isExecuting || readOnly}
            loading={isExecuting}
          >
            {t('可视化脚本.执行')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.停止脚本')}>
          <Button
            icon={<StopOutlined />}
            onClick={handleStop}
            disabled={!isExecuting}
          >
            {t('可视化脚本.停止')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.添加节点')}>
          <Button
            icon={<PlusOutlined />}
            onClick={() => setShowNodeSearch(true)}
            disabled={readOnly}
          >
            {t('可视化脚本.添加节点')}
          </Button>
        </Tooltip>
      </Space>
      
      <Space>
        <Tooltip title={t('可视化脚本.保存脚本')}>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={readOnly}
          >
            {t('可视化脚本.保存')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.设置')}>
          <Button
            type="text"
            icon={<SettingOutlined />}
          />
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.全屏')}>
          <Button
            type="text"
            icon={<FullscreenOutlined />}
          />
        </Tooltip>
      </Space>
    </div>
  );

  // 渲染画布
  const renderCanvas = () => (
    <div style={{ 
      position: 'relative',
      width: '100%',
      height: 'calc(100% - 49px)',
      backgroundColor: '#f8f9fa',
      backgroundImage: `
        radial-gradient(circle, #ddd 1px, transparent 1px)
      `,
      backgroundSize: '20px 20px'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          cursor: 'crosshair'
        }}
      />
      
      {/* 空状态提示 */}
      {scriptData.nodes.length === 0 && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: '#999'
        }}>
          <Title level={4} type="secondary">
            {t('可视化脚本.空画布标题')}
          </Title>
          <Text type="secondary">
            {t('可视化脚本.空画布描述')}
          </Text>
          <br />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setShowNodeSearch(true)}
            style={{ marginTop: '16px' }}
            disabled={readOnly}
          >
            {t('可视化脚本.添加第一个节点')}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div style={{ 
      height,
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      overflow: 'hidden',
      backgroundColor: '#fff'
    }}>
      {renderToolbar()}
      {renderCanvas()}
      
      {/* 节点搜索抽屉 */}
      <Drawer
        title={t('可视化脚本.选择节点')}
        placement="right"
        width={400}
        open={showNodeSearch}
        onClose={() => setShowNodeSearch(false)}
      >
        <NodeSearch
          nodes={availableNodes}
          favoriteNodes={favoriteNodes}
          recentNodes={recentNodes}
          onNodeSelect={handleNodeAdd}
          onToggleFavorite={handleToggleFavorite}
        />
      </Drawer>
    </div>
  );
};

export default VisualScriptEditor;
